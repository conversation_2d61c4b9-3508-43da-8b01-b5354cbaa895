import { VariableChange, VariableWithContext } from '@/types/variable';
import { determineVariableSaveContext } from './variableState';

/**
 * Tracks changes to variables for batch saving
 */
export class VariableChangeTracker {
  private changes: Map<string, VariableChange> = new Map();

  addChange(
    variableName: string,
    oldValue: any,
    newValue: any,
    originalVariable: VariableWithContext,
    nodeId?: number,
    nodeType?: 'category' | 'dataset'
  ): void {
    const saveContext = determineVariableSaveContext(originalVariable, nodeId, nodeType);
    
    this.changes.set(variableName, {
      variableName,
      oldValue,
      newValue,
      saveContext,
      originalVariable
    });
  }

  getChanges(): VariableChange[] {
    return Array.from(this.changes.values());
  }

  hasChanges(): boolean {
    return this.changes.size > 0;
  }

  clearChanges(): void {
    this.changes.clear();
  }

  removeChange(variableName: string): void {
    this.changes.delete(variableName);
  }
}

/**
 * Groups changes by their save context (template, category, dataset)
 */
export function groupChangesByContext(changes: VariableChange[]): Map<string, VariableChange[]> {
  const grouped = new Map<string, VariableChange[]>();
  
  for (const change of changes) {
    const key = change.saveContext.saveLevel === 'template' 
      ? 'template'
      : `${change.saveContext.saveLevel}-${change.saveContext.targetId}`;
    
    if (!grouped.has(key)) {
      grouped.set(key, []);
    }
    grouped.get(key)!.push(change);
  }
  
  return grouped;
}
