import { VariableWithContext, VariableSaveContext } from '@/types/variable';

/**
 * Determines where a variable should be saved based on its context and current state
 */
export function determineVariableSaveContext(
  variableWithContext: VariableWithContext,
  nodeId?: number,
  nodeType?: 'category' | 'dataset'
): VariableSaveContext {
  // If we're in a specific node context and the variable is not active at template level,
  // save to that context
  if (nodeId && nodeType && !variableWithContext.is_active) {
    return {
      saveLevel: nodeType,
      targetId: nodeId,
      targetType: nodeType
    };
  }

  // If the variable is currently active at template level, save to template
  if (variableWithContext.source_level === 'Template' && variableWithContext.is_active) {
    return {
      saveLevel: 'template'
    };
  }

  // If we have a node context, prefer saving there
  if (nodeId && nodeType) {
    return {
      saveLevel: nodeType,
      targetId: nodeId,
      targetType: nodeType
    };
  }

  // Default to template level
  return {
    saveLevel: 'template'
  };
}
