import React from 'react';
import { Box, Chip, Tooltip } from '@mui/material';
import { VariableWithContext } from '@/types/variable';
import InputRenderer from './InputRenderer';
import { VariableStatusBadge } from './VariableStatusBadge';

interface VariableInputRendererProps {
  variable: VariableWithContext;
  onChange: (variableName: string, newValue: any) => void;
  template_data?: any;
  contextInfo?: {
    nodeId?: number;
    nodeType?: 'category' | 'dataset';
    nodeName?: string;
  };
  showStatusBadge?: boolean;
  variableState?: {
    primaryState: 'active' | 'overridden' | 'not-set' | 'defined-higher';
    secondaryState?: 'active' | 'overridden' | 'not-set';
    counts: {
      active: number;
      overridden: number;
      notSet: number;
      definedHigher: number;
      total: number;
    };
    activeVariable?: any;
    overriddenBy?: string[];
  };
  currentValue?: any; // Current value from state management (includes changes)
}

/**
 * Renders a Variable Tree variable as an input component
 * Converts VariableWithContext to TemplateVariable format and uses InputRenderer
 */
export const VariableInputRenderer: React.FC<VariableInputRendererProps> = ({
  variable,
  onChange,
  template_data,
  contextInfo,
  showStatusBadge = true,
  variableState,
  currentValue
}) => {
  // Convert VariableWithContext to TemplateVariable format
  // Use currentValue if provided (from state management), otherwise use original value
  const templateVariable = {
    name: variable.name,
    data: currentValue !== undefined ? currentValue : (variable.value || variable.data),
    links: variable.links || [],
    gui: variable.gui
  };



  // If no GUI config, show as status badge only
  if (!variable.gui?.component_id) {
    if (!showStatusBadge) return null;
    
    return (
      <Tooltip title={`${variable.name}: ${JSON.stringify(variable.value)}`}>
        <Chip
          label={variable.name}
          size="small"
          color={variable.is_active ? 'primary' : 'default'}
          variant={variable.is_overridden ? 'outlined' : 'filled'}
        />
      </Tooltip>
    );
  }

  return (
    <Box sx={{ mb: 2 }}>
      {/* Status indicator using proper VariableStatusBadge */}
      {showStatusBadge && variableState && (
        <Box sx={{ mb: 1 }}>
          <VariableStatusBadge
            variableName={variable.name}
            primaryState={variableState.primaryState}
            secondaryState={variableState.secondaryState}
            counts={variableState.counts}
            activeVariable={variableState.activeVariable}
            overriddenBy={variableState.overriddenBy}
            size="small"
            showTooltip={true}
          />
        </Box>
      )}

      {/* Input component */}
      <Box sx={{ position: 'relative' }}>
        <InputRenderer
          variable={templateVariable} // Use templateVariable which already has the correct currentValue
          template_data={template_data}
          onChange={onChange}
        />

        {/* Override indicator */}
        {variable.is_overridden && (
          <Box
            sx={{
              position: 'absolute',
              top: -8,
              right: -8,
              width: 16,
              height: 16,
              borderRadius: '50%',
              backgroundColor: 'warning.main',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '0.6rem',
              color: 'white',
              fontWeight: 'bold'
            }}
          >
            !
          </Box>
        )}
      </Box>
    </Box>
  );
};



export default VariableInputRenderer;
