import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  CircularProgress,
  Alert,
  Paper,
  Grid,
  Button,
  Snackbar
} from '@mui/material';
import { RichTreeView } from '@mui/x-tree-view/RichTreeView';
import {
  ExpandMore as ExpandMoreIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import { useVariableTree } from '../../lib/hooks/useVariableTree';
import { useVariableTreeState } from '../../lib/hooks/useVariableTreeState';
import { VariableStatusBadge } from './VariableStatusBadge';
import { VariableInputRenderer } from './VariableInputRenderer';
import { VariableFocusIndicator } from './VariableFocusIndicator';

interface VariableTreeViewProps {
  templateId: number;
  onVariableSelect?: (variableName: string) => void;
}

interface TreeItemData {
  id: string;
  label: string;
  type: 'category' | 'dataset';
  variables?: any[];
  children?: TreeItemData[];
  nodeId?: number;
  datasetId?: number;
}

// Build tree structure from the new hierarchical API response
const buildTreeFromVariableData = (variableTreeNodes: any[]): TreeItemData[] => {
  const convertNode = (node: any): TreeItemData => {
    const treeItem: TreeItemData = {
      id: `category-${node.id}`,
      label: node.description ? `${node.name} (${node.description})` : node.name,
      type: 'category',
      variables: node.variables || [],
      children: [],
      nodeId: node.id
    };

    // Add datasets as children if they exist
    if (node.datasets && Array.isArray(node.datasets)) {
      node.datasets.forEach((dataset: any) => {
        const datasetItem: TreeItemData = {
          id: `dataset-${dataset.id}`,
          label: dataset.description ? `${dataset.name} (${dataset.description})` : dataset.name,
          type: 'dataset',
          variables: dataset.variables || [],
          nodeId: node.id,
          datasetId: dataset.id
        };
        treeItem.children!.push(datasetItem);
      });
    }

    // Add child categories recursively if they exist
    if (node.children && Array.isArray(node.children)) {
      node.children.forEach((childNode: any) => {
        const childItem = convertNode(childNode);
        treeItem.children!.push(childItem);
      });
    }

    return treeItem;
  };

  return variableTreeNodes.map(convertNode);
};

// Helper function to sort tree items recursively
const sortTreeItems = (items: TreeItemData[]): TreeItemData[] => {
  return items
    .sort((a, b) => a.label.localeCompare(b.label))
    .map(item => ({
      ...item,
      children: item.children ? sortTreeItems(item.children) : undefined
    }));
};

export const VariableTreeView: React.FC<VariableTreeViewProps> = ({
  templateId,
  onVariableSelect
}) => {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [selectedItems, setSelectedItems] = useState<string | null>(null);
  const [selectedNode, setSelectedNode] = useState<TreeItemData | null>(null);
  const [treeItems, setTreeItems] = useState<TreeItemData[]>([]);
  const [treeLoading, setTreeLoading] = useState(false);
  const [treeError, setTreeError] = useState<string | null>(null);
  const [showInputs, setShowInputs] = useState(true);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [showErrorSnackbar, setShowErrorSnackbar] = useState(false);

  const {
    data,
    loading,
    error,
    refetch,
    getVariablesByName,
    getVariableState,
    getVariableStateForContext,
    focusVariable,
    unfocusVariable,
    isVariableFocused,
    focusedVariable
  } = useVariableTree({ templateId });

  // State management for variable changes
  const {
    hasChanges,
    updateVariable,
    resetAllChanges,
    saveChanges,
    isSaving,
    saveError,
    getVariableValue,
    isVariableChanged
  } = useVariableTreeState({
    templateId,
    onSaveSuccess: () => {
      console.log('Variables saved successfully');
      setSaveSuccess(true);
      // Refetch the variable tree data to reflect changes
      refetch();
    },
    onSaveError: (error) => {
      console.error('Failed to save variables:', error);
      setShowErrorSnackbar(true);
    }
  });
  console.log('Variable tree data:', data);
  // Build tree from variable tree data when it's available
  
  useEffect(() => {
    if (data?.tree) {
      setTreeLoading(true);
      try {
        // Build tree directly from variable tree data
        const hierarchicalTree = buildTreeFromVariableData(data.tree);
        console.log('Built hierarchical tree:', hierarchicalTree);
        setTreeItems(sortTreeItems(hierarchicalTree));
        
        // Auto-expand root categories to show the hierarchy
        const rootCategoryIds = hierarchicalTree.map(item => item.id);
        console.log('Auto-expanding categories:', rootCategoryIds);
        setExpandedItems(rootCategoryIds);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
        setTreeError(errorMessage);
        console.error('Error building tree:', err);
      } finally {
        setTreeLoading(false);
        console.log('Tree items:', treeItems);
      }
    }
  }, [data]);

  // Helper function to collect all unique variable names for a context (including inherited)
  const getAllVariablesForContext = React.useCallback((nodeId?: number, datasetId?: number) => {
    if (!data) return [];

    const allVariableNames = new Set<string>();

    // Add template variables
    data.template_variables.forEach((v: any) => allVariableNames.add(v.name));

    // Find the path to the current context
    const findNodePath = (nodes: any[], targetNodeId: number): any[] => {
      for (const node of nodes) {
        if (node.id === targetNodeId) {
          return [node];
        }
        if (node.children) {
          const childPath = findNodePath(node.children, targetNodeId);
          if (childPath.length > 0) {
            return [node, ...childPath];
          }
        }
      }
      return [];
    };

    if (nodeId) {
      const nodePath = findNodePath(data.tree, nodeId);

      // Add variables from all nodes in the path (inheritance chain)
      nodePath.forEach(node => {
        if (node.variables) {
          node.variables.forEach((v: any) => allVariableNames.add(v.name));
        }
      });

      // If we're looking at a specific dataset, add its variables too
      if (datasetId && nodePath.length > 0) {
        const parentNode = nodePath[nodePath.length - 1];
        const targetDataset = parentNode.datasets?.find((d: any) => d.id === datasetId);
        if (targetDataset && targetDataset.variables) {
          targetDataset.variables.forEach((v: any) => allVariableNames.add(v.name));
        }
      }
    }

    return Array.from(allVariableNames).sort();
  }, [data]);

  // Find selected node when selection changes
  React.useEffect(() => {
    if (selectedItems && treeItems.length > 0) {
      const findNode = (items: TreeItemData[], id: string): TreeItemData | null => {
        for (const item of items) {
          if (item.id === id) return item;
          if (item.children) {
            const found = findNode(item.children, id);
            if (found) return found;
          }
        }
        return null;
      };
      setSelectedNode(findNode(treeItems, selectedItems));
    } else {
      setSelectedNode(null);
    }
  }, [selectedItems, treeItems]);

  // Handle ESC key to unfocus variable
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && focusedVariable) {
        unfocusVariable();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [focusedVariable, unfocusVariable]);

  if (loading || treeLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || treeError) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Fehler beim Laden der Daten: {error || treeError}
        <Box sx={{ mt: 1 }}>
          <Chip
            label="Erneut versuchen"
            onClick={refetch}
            size="small"
            variant="outlined"
          />
        </Box>
      </Alert>
    );
  }

  if (!data && treeItems.length === 0) {
    return (
      <Alert severity="info" sx={{ m: 2 }}>
        Keine Daten verfügbar
      </Alert>
    );
  }

  // Handle click on empty area to unfocus variable
  const handleBackgroundClick = (event: React.MouseEvent) => {
    // Only unfocus if clicking directly on the Paper background
    if (event.target === event.currentTarget && focusedVariable) {
      unfocusVariable();
    }
  };

  return (
    <Paper 
      sx={{ p: 2, height: '100%' }} 
      onClick={handleBackgroundClick}
    >
      {/* Header with Save Button */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Variablen-Baum (Template ID: {templateId})
        </Typography>

        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          {/* Save/Reset Controls */}
          {hasChanges && (
            <>
              <Button
                variant="contained"
                size="small"
                startIcon={<SaveIcon />}
                onClick={saveChanges}
                disabled={isSaving}
                color="primary"
              >
                {isSaving ? 'Speichern...' : 'Speichern'}
              </Button>
              <Button
                variant="outlined"
                size="small"
                onClick={resetAllChanges}
                disabled={isSaving}
                color="secondary"
              >
                Zurücksetzen
              </Button>
            </>
          )}

          <Button
            variant="outlined"
            size="small"
            onClick={() => setShowInputs(!showInputs)}
          >
            {showInputs ? 'STATUS-BADGES' : 'INPUT-KOMPONENTEN'}
          </Button>
        </Box>
      </Box>

      {/* Save Error */}
      {saveError && (
        <Alert severity="error" sx={{ mb: 2 }}>
          Fehler beim Speichern: {saveError}
        </Alert>
      )}

      {/* Template Variables */}
      {data && data.template_variables.filter((variable: any) => {
        return showInputs ? variable.gui?.component_id : true;
      }).length > 0 && (
        <Accordion defaultExpanded sx={{ mb: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle1">
              Template-Variablen ({data.template_variables.filter((variable: any) => {
                return showInputs ? variable.gui?.component_id : true;
              }).length}{showInputs ? ' mit GUI' : ''})
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {data.template_variables
                .filter((variable: any) => {
                  // In INPUT-KOMPONENTEN mode: only show variables with GUI components
                  // In STATUS-BADGES mode: show all variables
                  return showInputs ? variable.gui?.component_id : true;
                })
                .map((variable: any, index: number) => {
                  const state = getVariableState(variable.name);
                  if (showInputs && variable.gui?.component_id) {
                    return (
                      <VariableInputRenderer
                        key={index}
                        variable={variable}
                        onChange={(variableName: string, newValue: any) => {
                          updateVariable(variableName, newValue, variable);
                        }}
                        template_data={data}
                        showStatusBadge={true}
                        variableState={state}
                        currentValue={getVariableValue(variable.name, variable.value || variable.data)}
                      />
                    );
                  } else {
                    return (
                      <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                        <Box 
                          onClick={(e) => {
                            e.stopPropagation();
                            if (isVariableFocused(variable.name)) {
                              unfocusVariable();
                            } else {
                              focusVariable(variable.name);
                            }
                          }}
                          sx={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                        >
                          <VariableStatusBadge
                            variableName={variable.name}
                            primaryState={state.primaryState}
                            secondaryState={state.secondaryState}
                            counts={state.counts}
                            activeVariable={state.activeVariable}
                            overriddenBy={state.overriddenBy}
                          />
                          <VariableFocusIndicator
                            isVisible={isVariableFocused(variable.name)}
                            primaryState={state.primaryState}
                          />
                        </Box>
                      </Box>
                    );
                  }
                })}
            </Box>
          </AccordionDetails>
        </Accordion>
      )}

      {/* Two-column layout */}
      <Grid container spacing={2} sx={{ height: 'calc(100% - 200px)' }}>
        {/* Left column: Tree */}
        <Grid item xs={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="subtitle1" gutterBottom>
              Kategorie-Hierarchie
            </Typography>
            {treeItems.length > 0 ? (
              <RichTreeView
                items={treeItems}
                expandedItems={expandedItems}
                onExpandedItemsChange={(event, itemIds) => setExpandedItems(itemIds)}
                selectedItems={selectedItems}
                onSelectedItemsChange={(event, itemId) => setSelectedItems(itemId)}
                sx={{
                  flexGrow: 1,
                  maxWidth: '100%',
                  overflowY: 'auto',
                  bgcolor: 'background.paper',
                  borderRadius: 1,
                  p: 1
                }}
              />
            ) : (
              <Alert severity="info">
                Keine Kategorien gefunden
              </Alert>
            )}
          </Paper>
        </Grid>

        {/* Right column: Variables */}
        <Grid item xs={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="subtitle1" gutterBottom>
              Variablen
            </Typography>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
              🟢 Aktiv • 🟠 Überschrieben • 🔘 Von höherer Ebene geerbt • 🔴 Nicht gesetzt
            </Typography>
            {selectedNode ? (
              <Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {selectedNode.type === 'dataset' ? 'Dataset' : 'Kategorie'}: {selectedNode.label}
                </Typography>
                {(() => {
                  // Get all available variables for this context (including inherited)
                  const nodeId = selectedNode.nodeId;
                  const datasetId = selectedNode.datasetId;
                  const allVariableNames = getAllVariablesForContext(nodeId, datasetId);

                  return allVariableNames.length > 0 ? (
                    <Box sx={{
                      display: 'flex',
                      flexWrap: 'wrap',
                      gap: 1,
                      maxHeight: '400px',
                      overflowY: 'auto',
                      p: 1
                    }}>
                      {allVariableNames
                        .map(variableName => {
                          const allVariables = getVariablesByName(variableName);
                          const variable = allVariables.find((v: any) =>
                            (nodeId && v.source_level === 'Category') ||
                            (datasetId && v.source_level === 'Dataset') ||
                            v.source_level === 'Template'
                          );

                          // Debug logging for GNSSES variable
                          if (variableName === 'GNSSES') {
                            console.log('GNSSES variable debug:', {
                              variableName,
                              allVariables,
                              selectedVariable: variable,
                              nodeId,
                              datasetId,
                              hasGui: variable?.gui?.component_id
                            });
                          }

                          return {
                            name: variableName,
                            state: getVariableStateForContext(variableName, nodeId, datasetId),
                            variable
                          };
                        })
                        .filter(({ variable }) => {
                          // In INPUT-KOMPONENTEN mode: only show variables with GUI components
                          // In STATUS-BADGES mode: show all variables
                          return showInputs ? variable?.gui?.component_id : true;
                        })
                        .sort((a, b) => {
                          // Sort by state priority: active > overridden > defined-higher > not-set
                          const stateOrder = { 'active': 0, 'overridden': 1, 'defined-higher': 2, 'not-set': 3 };
                          const aOrder = stateOrder[a.state.primaryState] || 3;
                          const bOrder = stateOrder[b.state.primaryState] || 3;
                          if (aOrder !== bOrder) return aOrder - bOrder;
                          // If same state, sort alphabetically
                          return a.name.localeCompare(b.name);
                        })
                        .map(({ name, state, variable }, index) => {
                          if (showInputs && variable?.gui?.component_id) {
                            return (
                              <VariableInputRenderer
                                key={index}
                                variable={variable}
                                onChange={(variableName: string, newValue: any) => {
                                  // Use datasetId for datasets, nodeId for categories
                                  const contextId = selectedNode.type === 'dataset' ? datasetId : nodeId;
                                  updateVariable(variableName, newValue, variable, contextId, selectedNode.type);
                                }}
                                template_data={data}
                                contextInfo={{
                                  nodeId,
                                  nodeType: selectedNode.type,
                                  nodeName: selectedNode.label
                                }}
                                showStatusBadge={true}
                                variableState={state}
                                currentValue={getVariableValue(variable.name, variable.value || variable.data)}
                              />
                            );
                          } else {
                            return (
                              <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                                <Box 
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    if (isVariableFocused(name)) {
                                      unfocusVariable();
                                    } else {
                                      focusVariable(name);
                                    }
                                  }}
                                  sx={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                                >
                                  <VariableStatusBadge
                                    variableName={name}
                                    primaryState={state.primaryState}
                                    secondaryState={state.secondaryState}
                                    counts={state.counts}
                                    activeVariable={state.activeVariable}
                                    overriddenBy={state.overriddenBy}
                                  />
                                  <VariableFocusIndicator
                                    isVisible={isVariableFocused(name)}
                                    primaryState={state.primaryState}
                                  />
                                </Box>
                              </Box>
                            );
                          }
                        })}
                    </Box>
                  ) : (
                    <Alert severity="info">
                      Keine Variablen verfügbar für diesen {selectedNode.type === 'dataset' ? 'Dataset' : 'diese Kategorie'}
                    </Alert>
                  );
                })()}
              </Box>
            ) : (
              <Alert severity="info">
                Wählen Sie ein Dataset oder eine Kategorie aus dem Baum aus, um die Variablen anzuzeigen
              </Alert>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Save Success Message */}
      {saveSuccess && (
        <Alert severity="success" sx={{ mt: 2 }}>
          Änderungen erfolgreich gespeichert
        </Alert>
      )}

      {/* Error Snackbar */}
      <Snackbar
        open={showErrorSnackbar}
        autoHideDuration={6000}
        onClose={() => setShowErrorSnackbar(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setShowErrorSnackbar(false)}
          severity="error"
          sx={{ width: '100%' }}
        >
          Fehler beim Speichern: {saveError}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default VariableTreeView;