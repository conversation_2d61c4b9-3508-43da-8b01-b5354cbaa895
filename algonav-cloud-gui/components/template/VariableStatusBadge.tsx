import React from 'react';
import { Chip, Tooltip, Box } from '@mui/material';
import { styled } from '@mui/material/styles';
import { getVariableStateColor, getVariableStateLabel, type VariableState } from '../../lib/utils/variableColors';

interface VariableStatusBadgeProps {
  variableName: string;
  primaryState: 'active' | 'overridden' | 'not-set' | 'defined-higher';
  secondaryState?: 'active' | 'overridden' | 'not-set';
  counts: {
    active: number;
    overridden: number;
    notSet: number;
    definedHigher: number;
    total: number;
  };
  activeVariable?: any;
  overriddenBy?: string[];
  size?: 'small' | 'medium';
  showTooltip?: boolean;
}

const StyledChip = styled(Chip)<{ 
  primarycolor: string; 
  secondarycolor?: string;
  hassecondary: boolean;
}>(({ theme, primarycolor, secondarycolor, hassecondary }) => ({
  position: 'relative',
  backgroundColor: primarycolor,
  color: theme.palette.getContrastText(primarycolor),
  '&::after': hassecondary ? {
    content: '""',
    position: 'absolute',
    top: 2,
    right: 2,
    width: 6,
    height: 6,
    borderRadius: '50%',
    backgroundColor: secondarycolor,
    border: `1px solid ${theme.palette.background.paper}`,
  } : {},
  '&:hover': {
    backgroundColor: primarycolor,
    filter: 'brightness(0.9)',
  },
}));

// Color and label functions are now imported from utils

const generateTooltipContent = (
  variableName: string,
  primaryState: string,
  counts: VariableStatusBadgeProps['counts'],
  activeVariable?: any,
  overriddenBy?: string[]
): string => {
  const { active, overridden, notSet, definedHigher, total } = counts;
  
  let content = `Variable: ${variableName}\n`;
  content += `Status: ${getVariableStateLabel(primaryState as any)}\n\n`;
  
  if (activeVariable) {
    content += `🟢 Aktive Variable:\n`;
    content += `   Pfad: ${activeVariable.path}\n`;
    content += `   Wert: ${JSON.stringify(activeVariable.variable.data)}\n`;
    if (primaryState === 'defined-higher') {
      content += `   (Geerbt von höherer Ebene)\n`;
    }
    content += `\n`;
  }
  
  if (overriddenBy && overriddenBy.length > 0) {
    content += `🟠 Überschrieben von:\n`;
    overriddenBy.forEach(path => {
      content += `   • ${path}\n`;
    });
    content += `\n`;
  }
  
  content += `Zusammenfassung (${total} Instanzen):\n`;
  if (active > 0) content += `• Aktiv: ${active}\n`;
  if (overridden > 0) content += `• Überschrieben: ${overridden}\n`;
  if (definedHigher > 0) content += `• Höhere Ebene: ${definedHigher}\n`;
  if (notSet > 0) content += `• Nicht gesetzt: ${notSet}\n`;
  
  return content;
};

export const VariableStatusBadge: React.FC<VariableStatusBadgeProps> = ({
  variableName,
  primaryState,
  secondaryState,
  counts,
  activeVariable,
  overriddenBy,
  size = 'small',
  showTooltip = true
}) => {
  const primaryColor = getVariableStateColor(primaryState);
  const secondaryColor = secondaryState ? getVariableStateColor(secondaryState) : undefined;
  const hasSecondary = Boolean(secondaryState);
  
  const badge = (
    <StyledChip
      label={variableName}
      size={size}
      primarycolor={primaryColor}
      secondarycolor={secondaryColor}
      hassecondary={hasSecondary}
      variant="filled"
    />
  );

  if (!showTooltip) {
    return badge;
  }

  return (
    <Tooltip
      title={
        <Box component="pre" sx={{ 
          whiteSpace: 'pre-line', 
          fontSize: '0.75rem',
          fontFamily: 'monospace'
        }}>
          {generateTooltipContent(variableName, primaryState, counts, activeVariable, overriddenBy)}
        </Box>
      }
      placement="top"
      arrow
    >
      {badge}
    </Tooltip>
  );
};

export default VariableStatusBadge;