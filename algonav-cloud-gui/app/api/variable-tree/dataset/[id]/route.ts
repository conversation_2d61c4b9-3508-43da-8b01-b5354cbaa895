import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { withAuth } from '@/lib/api/withAuth';

/**
 * PUT /api/variable-tree/dataset/[id]
 * Updates dataset variable overrides in the Variable Tree system
 * 
 * Body: { variableOverrides: { [variableName: string]: any } }
 * 
 * This endpoint handles variable override updates for datasets.
 * Dataset variable overrides are stored in datasets.variable_overrides field.
 */
export const PUT = withAuth(async (userId, request, { params }) => {
  try {
    const supabase = createClient();
    const { id } = params;
    const { variableOverrides } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Dataset ID is required' },
        { status: 400 }
      );
    }

    if (!variableOverrides || typeof variableOverrides !== 'object') {
      return NextResponse.json(
        { error: 'variableOverrides is required and must be an object' },
        { status: 400 }
      );
    }

    // First, get the current dataset to verify ownership and get current overrides
    const { data: currentDataset, error: fetchError } = await supabase
      .from('datasets')
      .select('variable_overrides, user_id')
      .eq('id', id)
      .single();

    if (fetchError) {
      console.error('Error fetching dataset:', fetchError);
      return NextResponse.json(
        { error: 'Dataset not found' },
        { status: 404 }
      );
    }

    // Verify ownership
    if (currentDataset.user_id !== userId) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get current variable overrides structure
    const currentOverrides = currentDataset.variable_overrides || { vars: [] };
    const currentVarsList = Array.isArray(currentOverrides.vars) ? currentOverrides.vars : [];

    // Update variables with new values (same approach as template API)
    const updatedVarsList = currentVarsList.map((variable: any) => {
      if (variableOverrides.hasOwnProperty(variable.name)) {
        return {
          ...variable,
          data: variableOverrides[variable.name]
        };
      }
      return variable;
    });

    // Add new variables that don't exist yet
    for (const [variableName, variableValue] of Object.entries(variableOverrides)) {
      const existingVariable = currentVarsList.find((v: any) => v.name === variableName);
      if (!existingVariable) {
        // Create new variable with minimal structure
        updatedVarsList.push({
          name: variableName,
          data: variableValue,
          links: [],
          gui: {
            component_id: 'TextInput', // Default component
            label: variableName,
            group: 'Variable Tree',
            order: 999
          }
        });
      }
    }

    // Create the updated overrides structure
    const updatedOverrides = {
      ...currentOverrides,
      vars: updatedVarsList
    };

    // Update the dataset with new variable overrides
    const { data: updatedDataset, error: updateError } = await supabase
      .from('datasets')
      .update({
        variable_overrides: updatedOverrides,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('user_id', userId)
      .select('id, name, variable_overrides')
      .single();

    if (updateError) {
      console.error('Error updating dataset variables:', updateError);
      return NextResponse.json(
        { error: 'Failed to update dataset variables' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: updatedDataset.id,
        name: updatedDataset.name,
        updatedVariables: Object.keys(variableOverrides),
        totalOverrides: Object.keys(updatedOverrides).length,
        variable_overrides: updatedOverrides
      }
    });

  } catch (error) {
    console.error('Dataset variable update error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
