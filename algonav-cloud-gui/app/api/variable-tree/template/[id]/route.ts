import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { withAuth } from '@/lib/api/withAuth';

/**
 * PUT /api/variable-tree/template/[id]
 * Updates template variables in the Variable Tree system
 * 
 * Body: { variableOverrides: { [variableName: string]: any } }
 * 
 * This endpoint handles variable updates for the Variable Tree system,
 * which stores variables differently from the legacy template system.
 * Template variables are stored in global_job_templates.vars field.
 */
export const PUT = withAuth(async (userId, request, { params }) => {
  try {
    const supabase = createClient();
    const { id } = params;
    const { variableOverrides } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    if (!variableOverrides || typeof variableOverrides !== 'object') {
      return NextResponse.json(
        { error: 'variableOverrides is required and must be an object' },
        { status: 400 }
      );
    }

    // First, get the current template to verify ownership and get current vars
    const { data: currentTemplate, error: fetchError } = await supabase
      .from('global_job_templates')
      .select('vars, user_id')
      .eq('id', id)
      .single();

    if (fetchError) {
      console.error('Error fetching template:', fetchError);
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Verify ownership
    if (currentTemplate.user_id !== userId) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get current vars structure
    const currentVars = currentTemplate.vars || { vars: [] };
    const currentVarsList = Array.isArray(currentVars.vars) ? currentVars.vars : [];

    // Update variables with new values
    const updatedVarsList = currentVarsList.map((variable: any) => {
      if (variableOverrides.hasOwnProperty(variable.name)) {
        return {
          ...variable,
          data: variableOverrides[variable.name]
        };
      }
      return variable;
    });

    // Add new variables that don't exist yet
    for (const [variableName, variableValue] of Object.entries(variableOverrides)) {
      const existingVariable = currentVarsList.find((v: any) => v.name === variableName);
      if (!existingVariable) {
        // Create new variable with minimal structure
        updatedVarsList.push({
          name: variableName,
          data: variableValue,
          links: [],
          gui: {
            component_id: 'TextInput', // Default component
            label: variableName,
            group: 'Variable Tree',
            order: 999
          }
        });
      }
    }

    // Update the template with new vars
    const { data: updatedTemplate, error: updateError } = await supabase
      .from('global_job_templates')
      .update({
        vars: { vars: updatedVarsList }
      })
      .eq('id', id)
      .eq('user_id', userId)
      .select('*')
      .single();

    if (updateError) {
      console.error('Error updating template variables:', updateError);
      return NextResponse.json(
        { error: 'Failed to update template variables' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: updatedTemplate.id,
        updatedVariables: Object.keys(variableOverrides),
        totalVariables: updatedVarsList.length
      }
    });

  } catch (error) {
    console.error('Template variable update error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
