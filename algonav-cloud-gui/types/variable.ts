/**
 * Variable-related type definitions
 * Centralized types for the variable system
 */

export interface VariableWithContext {
  name: string;
  source_level: 'Template' | 'Category' | 'Dataset';
  is_active: boolean;
  is_overridden: boolean;
  value: any;
  data?: any; // Alias for value
  gui_config?: any;
  gui?: any; // GUI configuration from database
  links?: any[];
}

export interface VariableTreeNode {
  id: number;
  name: string;
  description?: string;
  type: 'category' | 'dataset';
  level?: number;
  variables: VariableWithContext[];
  datasets?: VariableTreeNode[];
  children?: VariableTreeNode[];
}

export interface VariableTreeResponse {
  template_id: number;
  template_variables: VariableWithContext[];
  tree: VariableTreeNode[];
}

/**
 * Creates a variable change record for tracking modifications
 */
export interface VariableChange {
  variableName: string;
  oldValue: any;
  newValue: any;
  saveContext: {
    saveLevel: 'template' | 'category' | 'dataset';
    targetId?: number;
    targetType?: 'category' | 'dataset';
  };
  originalVariable: VariableWithContext;
}

/**
 * Save context for determining where a variable should be saved
 */
export interface VariableSaveContext {
  saveLevel: 'template' | 'category' | 'dataset';
  targetId?: number;
  targetType?: 'category' | 'dataset';
}
